/**
 * Role Custom Admin Para Talepleri JavaScript
 */

(function($) {
    'use strict';

    // Para çekme talebini onayla
    $('.approve-withdraw').on('click', function() {
        const requestId = $(this).data('id');
        const button = $(this);

        if (!confirm(roleCustomAdminWithdraw.strings.confirm_approve)) {
            return;
        }

        button.addClass('loading').prop('disabled', true);

        $.ajax({
            url: roleCustomAdminWithdraw.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_approve_withdraw',
                nonce: roleCustomAdminWithdraw.nonce,
                request_id: requestId
            },
            success: function(response) {
                if (response.success) {
                    showNotice(roleCustomAdminWithdraw.strings.success, 'success');

                    // Satırı güncelle
                    const row = button.closest('tr');
                    row.find('.withdraw-status').replaceWith('<span class="withdraw-status approved">Onaylandı</span>');
                    row.find('td:last-child').html('<em>İşlem tamamlandı</em>');

                    // Sayfa sayılarını güncelle
                    updateStatusCounts();
                } else {
                    showNotice(response.data || roleCustomAdminWithdraw.strings.error, 'error');
                }
            },
            error: function(xhr, status, error) {
                showNotice(roleCustomAdminWithdraw.strings.error, 'error');
            },
            complete: function() {
                button.removeClass('loading').prop('disabled', false);
            }
        });
    });

    // Para çekme talebini reddet
    $('.reject-withdraw').on('click', function() {
        const requestId = $(this).data('id');
        const button = $(this);

        if (!confirm(roleCustomAdminWithdraw.strings.confirm_reject)) {
            return;
        }

        button.addClass('loading').prop('disabled', true);

        $.ajax({
            url: roleCustomAdminWithdraw.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_reject_withdraw',
                nonce: roleCustomAdminWithdraw.nonce,
                request_id: requestId
            },
            success: function(response) {
                if (response.success) {
                    showNotice(roleCustomAdminWithdraw.strings.success, 'success');

                    // Satırı güncelle
                    const row = button.closest('tr');
                    row.find('.withdraw-status').replaceWith('<span class="withdraw-status rejected">Reddedildi</span>');
                    row.find('td:last-child').html('<em>İşlem tamamlandı</em>');

                    // Sayfa sayılarını güncelle
                    updateStatusCounts();
                } else {
                    showNotice(response.data || roleCustomAdminWithdraw.strings.error, 'error');
                }
            },
            error: function(xhr, status, error) {
                showNotice(roleCustomAdminWithdraw.strings.error, 'error');
            },
            complete: function() {
                button.removeClass('loading').prop('disabled', false);
            }
        });
    });

    // Para çekme talebini sil
    $('.delete-withdraw').on('click', function() {
        const requestId = $(this).data('id');
        const button = $(this);

        if (!confirm(roleCustomAdminWithdraw.strings.confirm_delete)) {
            return;
        }

        button.addClass('loading').prop('disabled', true);

        $.ajax({
            url: roleCustomAdminWithdraw.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_delete_withdraw',
                nonce: roleCustomAdminWithdraw.nonce,
                request_id: requestId
            },
            success: function(response) {
                if (response.success) {
                    showNotice(roleCustomAdminWithdraw.strings.success, 'success');

                    // Satırı tamamen kaldır
                    const row = button.closest('tr');
                    const nextRow = row.next('tr'); // Not satırı varsa

                    row.fadeOut(300, function() {
                        $(this).remove();
                        // Eğer not satırı varsa onu da kaldır
                        if (nextRow.length && nextRow.find('td').length === 1) {
                            nextRow.fadeOut(300, function() {
                                $(this).remove();
                            });
                        }
                    });

                    // Sayfa sayılarını güncelle
                    updateStatusCounts();
                } else {
                    showNotice(response.data || roleCustomAdminWithdraw.strings.error, 'error');
                }
            },
            error: function(xhr, status, error) {
                showNotice(roleCustomAdminWithdraw.strings.error, 'error');
            },
            complete: function() {
                button.removeClass('loading').prop('disabled', false);
            }
        });
    });

    /**
     * Bildirim göster
     */
    function showNotice(message, type) {
        // Mevcut bildirimleri kaldır
        $('.role-custom-admin-notice').remove();

        const notice = $('<div class="role-custom-admin-notice ' + type + '">' + message + '</div>');
        $('.role-custom-admin-withdraw h1').after(notice);

        // 5 saniye sonra otomatik kaldır
        setTimeout(function() {
            notice.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
    }

    /**
     * Durum sayılarını güncelle
     */
    function updateStatusCounts() {
        // Mevcut tablodaki durumları say
        const pendingCount = $('.withdraw-status.pending').length;
        const approvedCount = $('.withdraw-status.approved').length;
        const rejectedCount = $('.withdraw-status.rejected').length;
        const totalCount = pendingCount + approvedCount + rejectedCount;

        // Filtrelerdeki sayıları güncelle
        $('.subsubsub a[href*="status=all"] .count').text('(' + totalCount + ')');
        $('.subsubsub a[href*="status=pending"] .count').text('(' + pendingCount + ')');
        $('.subsubsub a[href*="status=approved"] .count').text('(' + approvedCount + ')');
        $('.subsubsub a[href*="status=rejected"] .count').text('(' + rejectedCount + ')');
    }

    /**
     * Tablo sıralama
     */
    $('.withdraw-requests-table th.sortable').on('click', function() {
        const table = $(this).closest('table');
        const columnIndex = $(this).index();
        const isAsc = $(this).hasClass('asc');
        
        // Tüm sıralama sınıflarını kaldır
        table.find('th').removeClass('asc desc');
        
        // Yeni sıralama sınıfını ekle
        $(this).addClass(isAsc ? 'desc' : 'asc');
        
        // Satırları sırala
        const rows = table.find('tbody tr').get();
        
        rows.sort(function(a, b) {
            const aValue = $(a).find('td').eq(columnIndex).text().trim();
            const bValue = $(b).find('td').eq(columnIndex).text().trim();
            
            // Sayısal değerler için özel kontrol
            if (columnIndex === 1) { // Miktar kolonu
                const aNum = parseFloat(aValue.replace(/[^\d.,]/g, '').replace(',', '.'));
                const bNum = parseFloat(bValue.replace(/[^\d.,]/g, '').replace(',', '.'));
                return isAsc ? bNum - aNum : aNum - bNum;
            }
            
            // Tarih kolonu için özel kontrol
            if (columnIndex === 2) {
                const aDate = new Date(aValue);
                const bDate = new Date(bValue);
                return isAsc ? bDate - aDate : aDate - bDate;
            }
            
            // Metin karşılaştırması
            if (aValue < bValue) return isAsc ? 1 : -1;
            if (aValue > bValue) return isAsc ? -1 : 1;
            return 0;
        });
        
        // Sıralanmış satırları tabloya ekle
        $.each(rows, function(index, row) {
            table.find('tbody').append(row);
        });
    });

    /**
     * Miktar formatı
     */
    function formatAmount(amount) {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY'
        }).format(amount);
    }

    /**
     * Tarih formatı
     */
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('tr-TR', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    /**
     * Tablo filtresi
     */
    function filterTable(searchTerm) {
        const rows = $('.withdraw-requests-table tbody tr');
        
        if (!searchTerm) {
            rows.show();
            return;
        }
        
        rows.each(function() {
            const rowText = $(this).text().toLowerCase();
            if (rowText.includes(searchTerm.toLowerCase())) {
                $(this).show();
            } else {
                $(this).hide();
            }
        });
    }

    /**
     * Arama kutusu (gelecekte eklenebilir)
     */
    if ($('#withdraw-search').length) {
        $('#withdraw-search').on('input', function() {
            const searchTerm = $(this).val();
            filterTable(searchTerm);
        });
    }

    /**
     * Toplu işlemler (gelecekte eklenebilir)
     */
    function handleBulkActions() {
        const selectedRows = $('.withdraw-requests-table input[type="checkbox"]:checked');
        const action = $('#bulk-action-selector').val();
        
        if (selectedRows.length === 0) {
            alert('Lütfen en az bir talep seçin.');
            return;
        }
        
        if (!action) {
            alert('Lütfen bir işlem seçin.');
            return;
        }
        
        const requestIds = [];
        selectedRows.each(function() {
            requestIds.push($(this).val());
        });
        
        // Toplu işlem AJAX çağrısı burada yapılabilir
        console.log('Bulk action:', action, 'Request IDs:', requestIds);
    }

    // Sayfa yüklendiğinde tablo sıralamasını etkinleştir
    $('.withdraw-requests-table th').each(function(index) {
        if (index < 4) { // İlk 4 kolon sıralanabilir
            $(this).addClass('sortable');
        }
    });

})(jQuery);
